openapi: 3.0.3
info:
  title: Techrar Gym - Customer Mobile API
  description: |
    Customer-facing mobile API for gym members to manage their accounts, view classes, book sessions, and manage subscriptions.

    ## Authentication
    - Customer authentication uses email prefixing for multi-tenancy
    - All endpoints require `merchant-id` header for tenant isolation
    - Bearer token authentication for protected endpoints

    ## Base Response Format
    All endpoints return a standardized response format:
    ```json
    {
      "success": boolean,
      "message": string,
      "data": object | null,
      "errors": object | null
    }
    ```
  version: 1.0.0
  contact:
    name: Techrar Gym API Support
    email: <EMAIL>
servers:
  - url: https://techrar-gym-production.up.railway.app
    description: Production server

paths:
  # Customer Authentication Endpoints
  /api/customer-auth/login:
    post:
      tags:
        - Customer Authentication
      summary: Customer login
      description: Authenticates gym customers and establishes session with JWT tokens
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CustomerLoginRequest"
            example:
              email: "<EMAIL>"
              password: "password123"
      parameters:
        - $ref: "#/components/parameters/MerchantIdHeader"
      responses:
        "200":
          description: Login successful
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CustomerAuthResponse"
              example:
                success: true
                message: "Login successful"
                data:
                  customer:
                    id: "123e4567-e89b-12d3-a456-************"
                    full_name: "Ahmed Ali"
                    email: "<EMAIL>"
                    phone: "+************"
                    status: "active"
                    merchant_id: "456e7890-e89b-12d3-a456-************"
                  session:
                    access_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                    refresh_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                    expires_in: 3600
                errors: null
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "500":
          $ref: "#/components/responses/InternalServerError"

  /api/customer-auth/register:
    post:
      tags:
        - Customer Authentication
      summary: Customer registration
      description: Creates new customer account with email verification
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CustomerRegisterRequest"
            example:
              full_name: "Ahmed Ali"
              email: "<EMAIL>"
              password: "password123"
              phone: "+************"
              gender: "male"
              date_of_birth: "1990-01-15"
      parameters:
        - $ref: "#/components/parameters/MerchantIdHeader"
      responses:
        "201":
          description: Registration successful
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CustomerAuthResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "409":
          $ref: "#/components/responses/Conflict"
        "500":
          $ref: "#/components/responses/InternalServerError"

  /api/customer-auth/profile:
    get:
      tags:
        - Customer Authentication
      summary: Get customer profile
      description: Retrieves complete customer profile information
      security:
        - BearerAuth: []
      parameters:
        - $ref: "#/components/parameters/MerchantIdHeader"
      responses:
        "200":
          description: Profile retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CustomerProfileResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"

    put:
      tags:
        - Customer Authentication
      summary: Update customer profile
      description: Updates customer profile information (excludes email and password)
      security:
        - BearerAuth: []
      parameters:
        - $ref: "#/components/parameters/MerchantIdHeader"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CustomerProfileUpdateRequest"
            example:
              full_name: "Ahmed Ali Al-Rashid"
              phone: "+************"
              gender: "male"
              date_of_birth: "1990-01-15"
              profile_image_url: "https://example.com/profile.jpg"
      responses:
        "200":
          description: Profile updated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CustomerProfileResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "500":
          $ref: "#/components/responses/InternalServerError"

  /api/customer-auth/refresh:
    post:
      tags:
        - Customer Authentication
      summary: Refresh authentication tokens
      description: Refreshes access and refresh tokens using valid refresh token
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RefreshTokenRequest"
            example:
              refresh_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
      parameters:
        - $ref: "#/components/parameters/MerchantIdHeader"
      responses:
        "200":
          description: Tokens refreshed successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RefreshTokenResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "500":
          $ref: "#/components/responses/InternalServerError"

  /api/customer-auth/logout:
    post:
      tags:
        - Customer Authentication
      summary: Customer logout
      description: Ends customer session and invalidates tokens
      security:
        - BearerAuth: []
      parameters:
        - $ref: "#/components/parameters/MerchantIdHeader"
      responses:
        "200":
          description: Logout successful
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StandardResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "500":
          $ref: "#/components/responses/InternalServerError"

  /api/customer-auth/reset-password:
    post:
      tags:
        - Customer Authentication
      summary: Reset password
      description: Initiates password reset process by sending reset email
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ResetPasswordRequest"
            example:
              email: "<EMAIL>"
      parameters:
        - $ref: "#/components/parameters/MerchantIdHeader"
      responses:
        "200":
          description: Password reset email sent
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StandardResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"

  # Public API Endpoints (Mobile App)
  /api/public/classes:
    get:
      tags:
        - Classes
      summary: Get available gym classes
      description: Retrieves list of available gym classes with upcoming sessions and trainer information
      parameters:
        - $ref: "#/components/parameters/MerchantIdHeader"
        - name: branch_id
          in: query
          description: Filter classes by branch
          schema:
            type: string
            format: uuid
        - name: trainer_id
          in: query
          description: Filter classes by trainer
          schema:
            type: string
            format: uuid
        - name: difficulty
          in: query
          description: Filter classes by difficulty level
          schema:
            type: string
            enum: [beginner, intermediate, advanced]
        - name: is_personal_training
          in: query
          description: Filter for personal training sessions
          schema:
            type: boolean
        - name: from_date
          in: query
          description: Filter sessions from this date (ISO format)
          schema:
            type: string
            format: date-time
        - name: to_date
          in: query
          description: Filter sessions to this date (ISO format)
          schema:
            type: string
            format: date-time
      responses:
        "200":
          description: Classes retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ClassesResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "500":
          $ref: "#/components/responses/InternalServerError"

  /api/public/bookings:
    get:
      tags:
        - Bookings
      summary: Get customer bookings
      description: Retrieves customer's class bookings with filtering options
      parameters:
        - $ref: "#/components/parameters/MerchantIdHeader"
        - name: member_id
          in: query
          required: true
          description: Customer member ID
          schema:
            type: string
            format: uuid
        - name: status
          in: query
          description: Filter by booking status
          schema:
            type: string
            enum: [confirmed, cancelled, attended, no_show, waitlisted]
        - name: timeframe
          in: query
          description: Filter by time (upcoming or past bookings)
          schema:
            type: string
            enum: [upcoming, past]
            default: upcoming
      responses:
        "200":
          description: Bookings retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BookingsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"

    post:
      tags:
        - Bookings
      summary: Create class booking
      description: Books a class session for the authenticated customer
      security:
        - BearerAuth: []
      parameters:
        - $ref: "#/components/parameters/MerchantIdHeader"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateBookingRequest"
            example:
              member_id: "123e4567-e89b-12d3-a456-************"
              session_id: "456e7890-e89b-12d3-a456-************"
              booking_notes: "First time attending this class"
      responses:
        "201":
          description: Booking created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateBookingResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"

  /api/public/subscription-plans:
    get:
      tags:
        - Subscription Plans
      summary: Get available subscription plans
      description: Retrieves list of available subscription plans for purchase
      parameters:
        - $ref: "#/components/parameters/MerchantIdHeader"
        - name: branch_id
          in: query
          description: Filter plans by branch
          schema:
            type: string
            format: uuid
        - name: is_active
          in: query
          description: Filter by active status
          schema:
            type: boolean
            default: true
      responses:
        "200":
          description: Subscription plans retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SubscriptionPlansResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "500":
          $ref: "#/components/responses/InternalServerError"

  /api/public/subscription-plans/{id}:
    get:
      tags:
        - Subscription Plans
      summary: Get subscription plan details
      description: Retrieves detailed information about a specific subscription plan
      parameters:
        - $ref: "#/components/parameters/MerchantIdHeader"
        - name: id
          in: path
          required: true
          description: Subscription plan ID
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: Subscription plan details retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SubscriptionPlanDetailsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"

  /api/public/subscriptions:
    get:
      tags:
        - Subscriptions
      summary: Get customer subscriptions
      description: Retrieves customer's subscription history and current subscriptions
      security:
        - BearerAuth: []
      parameters:
        - $ref: "#/components/parameters/MerchantIdHeader"
        - name: status
          in: query
          description: Filter by subscription status
          schema:
            type: string
            enum: [active, paused, cancelled, expired, frozen, pending]
      responses:
        "200":
          description: Subscriptions retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SubscriptionsResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "500":
          $ref: "#/components/responses/InternalServerError"

  /api/public/subscriptions/purchase:
    post:
      tags:
        - Subscriptions
      summary: Purchase subscription plan
      description: Purchases a new subscription plan for the customer
      security:
        - BearerAuth: []
      parameters:
        - $ref: "#/components/parameters/MerchantIdHeader"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PurchaseSubscriptionRequest"
            example:
              plan_id: "456e7890-e89b-12d3-a456-************"
              payment_method: "credit_card"
              auto_renew: true
              start_date: "2024-01-01T00:00"
      responses:
        "201":
          description: Subscription purchased successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PurchaseSubscriptionResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"

  /api/public/subscriptions/cancel:
    post:
      tags:
        - Subscriptions
      summary: Cancel subscription
      description: Requests subscription cancellation (requires staff approval)
      security:
        - BearerAuth: []
      parameters:
        - $ref: "#/components/parameters/MerchantIdHeader"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CancelSubscriptionRequest"
            example:
              subscription_id: "789e0123-e89b-12d3-a456-************"
              cancellation_reason: "Moving to another city"
              cancellation_date: "2024-01-15T00:00"
      responses:
        "200":
          description: Cancellation request submitted successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CancelSubscriptionResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"

  /api/public/subscriptions/freeze:
    post:
      tags:
        - Subscriptions
      summary: Freeze subscription
      description: Freezes active subscription using available freeze credits
      security:
        - BearerAuth: []
      parameters:
        - $ref: "#/components/parameters/MerchantIdHeader"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/FreezeSubscriptionRequest"
            example:
              subscription_id: "789e0123-e89b-12d3-a456-************"
              freeze_reason: "Traveling abroad"
              freeze_days: 30
              freeze_start_date: "2024-01-01T00:00"
      responses:
        "200":
          description: Subscription frozen successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FreezeSubscriptionResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"

  /api/public/subscriptions/unfreeze:
    post:
      tags:
        - Subscriptions
      summary: Unfreeze subscription
      description: Unfreezes frozen subscription back to active status
      security:
        - BearerAuth: []
      parameters:
        - $ref: "#/components/parameters/MerchantIdHeader"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UnfreezeSubscriptionRequest"
            example:
              subscription_id: "789e0123-e89b-12d3-a456-************"
              unfreeze_reason: "Back from travel"
      responses:
        "200":
          description: Subscription unfrozen successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UnfreezeSubscriptionResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token obtained from customer login or refresh endpoints

  parameters:
    MerchantIdHeader:
      name: merchant-id
      in: header
      required: true
      description: Merchant/Gym identifier for multi-tenant access
      schema:
        type: string
        format: uuid
      example: "789e0123-e89b-12d3-a456-************"

  schemas:
    # Base Response Schemas
    StandardResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        data:
          type: object
          nullable: true
        errors:
          type: object
          nullable: true
      required:
        - success
        - message
        - data
        - errors

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
        data:
          type: object
          nullable: true
          example: null
        errors:
          type: object
          nullable: true
      required:
        - success
        - message
        - data
        - errors

    # Customer Authentication Schemas
    CustomerLoginRequest:
      type: object
      properties:
        email:
          type: string
          format: email
          example: "<EMAIL>"
        password:
          type: string
          minLength: 6
          example: "password123"
      required:
        - email
        - password

    CustomerRegisterRequest:
      type: object
      properties:
        full_name:
          type: string
          minLength: 2
          maxLength: 100
          example: "Ahmed Ali"
        email:
          type: string
          format: email
          example: "<EMAIL>"
        password:
          type: string
          minLength: 6
          maxLength: 128
          example: "password123"
        phone:
          type: string
          pattern: '^(\+966|966|0)?(5[0-9]|1[1-9]|2[1-9]|3[1-9]|4[1-9]|7[1-9])[0-9]{7}$'
          example: "+************"
          nullable: true
        gender:
          type: string
          enum: [male, female, other]
          example: "male"
          nullable: true
        date_of_birth:
          type: string
          format: date
          example: "1990-01-15"
          nullable: true
        gym_id:
          type: string
          format: uuid
          nullable: true
      required:
        - full_name
        - email
        - password

    CustomerProfileUpdateRequest:
      type: object
      properties:
        full_name:
          type: string
          minLength: 2
          maxLength: 100
          example: "Ahmed Ali Al-Rashid"
        phone:
          type: string
          pattern: '^(\+966|966|0)?(5[0-9]|1[1-9]|2[1-9]|3[1-9]|4[1-9]|7[1-9])[0-9]{7}$'
          example: "+************"
          nullable: true
        gender:
          type: string
          enum: [male, female, other]
          example: "male"
          nullable: true
        date_of_birth:
          type: string
          format: date
          example: "1990-01-15"
          nullable: true
        profile_image_url:
          type: string
          format: uri
          example: "https://example.com/profile.jpg"
          nullable: true

    RefreshTokenRequest:
      type: object
      properties:
        refresh_token:
          type: string
          example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
      required:
        - refresh_token

    ResetPasswordRequest:
      type: object
      properties:
        email:
          type: string
          format: email
          example: "<EMAIL>"
      required:
        - email

    CustomerProfile:
      type: object
      properties:
        id:
          type: string
          format: uuid
        full_name:
          type: string
        email:
          type: string
          format: email
        phone:
          type: string
          nullable: true
        gender:
          type: string
          enum: [male, female, other]
          nullable: true
        date_of_birth:
          type: string
          format: date
          nullable: true
        profile_image_url:
          type: string
          format: uri
          nullable: true
        status:
          type: string
          enum: [active, suspended, inactive]
        merchant_id:
          type: string
          format: uuid
        created_at:
          type: string
          format: date-time
      required:
        - id
        - full_name
        - email
        - status
        - merchant_id
        - created_at

    SessionTokens:
      type: object
      properties:
        access_token:
          type: string
        refresh_token:
          type: string
        expires_in:
          type: integer
        token_type:
          type: string
          example: "bearer"
      required:
        - access_token
        - refresh_token
        - expires_in

    CustomerAuthResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
        data:
          type: object
          properties:
            customer:
              $ref: "#/components/schemas/CustomerProfile"
            session:
              $ref: "#/components/schemas/SessionTokens"
        errors:
          type: object
          nullable: true
      required:
        - success
        - message
        - data
        - errors

    CustomerProfileResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
        data:
          $ref: "#/components/schemas/CustomerProfile"
        errors:
          type: object
          nullable: true
      required:
        - success
        - message
        - data
        - errors

    RefreshTokenResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
        data:
          type: object
          properties:
            session:
              $ref: "#/components/schemas/SessionTokens"
            customer:
              $ref: "#/components/schemas/CustomerProfile"
        errors:
          type: object
          nullable: true
      required:
        - success
        - message
        - data
        - errors

    # Public API Schemas
    CreateBookingRequest:
      type: object
      properties:
        member_id:
          type: string
          format: uuid
        session_id:
          type: string
          format: uuid
        booking_notes:
          type: string
          maxLength: 500
          nullable: true
      required:
        - member_id
        - session_id

    Trainer:
      type: object
      properties:
        id:
          type: string
          format: uuid
        full_name:
          type: string
        profile_image_url:
          type: string
          format: uri
          nullable: true

    GymClass:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        description:
          type: string
          nullable: true
        duration:
          type: integer
          description: Duration in minutes
        capacity:
          type: integer
        price:
          type: number
          format: decimal
        difficulty:
          type: string
          enum: [beginner, intermediate, advanced]
        is_personal_training:
          type: boolean
        room:
          type: string
          nullable: true
        trainer:
          $ref: "#/components/schemas/Trainer"

    ClassSession:
      type: object
      properties:
        id:
          type: string
          format: uuid
        start_time:
          type: string
          format: date-time
        end_time:
          type: string
          format: date-time
        status:
          type: string
          enum: [scheduled, ongoing, completed, cancelled]
        booked_count:
          type: integer
        location:
          type: string
          nullable: true
        is_cancelled:
          type: boolean
        class:
          $ref: "#/components/schemas/GymClass"

    ClassBooking:
      type: object
      properties:
        id:
          type: string
          format: uuid
        member_id:
          type: string
          format: uuid
        session_id:
          type: string
          format: uuid
        status:
          type: string
          enum: [confirmed, cancelled, attended, no_show, waitlisted]
        booking_date:
          type: string
          format: date-time
        booking_notes:
          type: string
          nullable: true
        session:
          $ref: "#/components/schemas/ClassSession"
        created_at:
          type: string
          format: date-time

    SubscriptionPlanFeature:
      type: object
      properties:
        name:
          type: string
        name_alt:
          type: string
        value:
          type: string
        isNumeric:
          type: boolean
        isPremium:
          type: boolean
        icon:
          type: string
          nullable: true
      required:
        - name
        - name_alt
        - value

    SubscriptionPlan:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        name_alt:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        price:
          type: number
          format: decimal
        duration:
          type: integer
          description: Duration in days
        classes_per_week:
          type: integer
          nullable: true
        features:
          type: array
          items:
            $ref: "#/components/schemas/SubscriptionPlanFeature"
        freeze_days_credit:
          type: integer
        freeze_attempts_credit:
          type: integer
        is_active:
          type: boolean
        created_at:
          type: string
          format: date-time

    Subscription:
      type: object
      properties:
        id:
          type: string
          format: uuid
        member_id:
          type: string
          format: uuid
        plan_id:
          type: string
          format: uuid
        status:
          type: string
          enum: [active, paused, cancelled, expired, frozen, pending]
        start_date:
          type: string
          format: date-time
        end_date:
          type: string
          format: date-time
        auto_renew:
          type: boolean
        freeze_days_credit:
          type: integer
        freeze_attempts_credit:
          type: integer
        is_freezed_today:
          type: boolean
        plan:
          $ref: "#/components/schemas/SubscriptionPlan"
        created_at:
          type: string
          format: date-time

    # Request Schemas for Subscriptions
    PurchaseSubscriptionRequest:
      type: object
      properties:
        plan_id:
          type: string
          format: uuid
        payment_method:
          type: string
          example: "credit_card"
        auto_renew:
          type: boolean
          default: true
        start_date:
          type: string
          pattern: '^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$'
          example: "2024-01-01T00:00"
          nullable: true
      required:
        - plan_id

    CancelSubscriptionRequest:
      type: object
      properties:
        subscription_id:
          type: string
          format: uuid
        cancellation_reason:
          type: string
          maxLength: 500
        cancellation_date:
          type: string
          pattern: '^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$'
          example: "2024-01-15T00:00"
          nullable: true
      required:
        - subscription_id
        - cancellation_reason

    FreezeSubscriptionRequest:
      type: object
      properties:
        subscription_id:
          type: string
          format: uuid
        freeze_reason:
          type: string
          maxLength: 500
        freeze_days:
          type: integer
          minimum: 1
          maximum: 365
        freeze_start_date:
          type: string
          pattern: '^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$'
          example: "2024-01-01T00:00"
          nullable: true
      required:
        - subscription_id
        - freeze_reason
        - freeze_days

    UnfreezeSubscriptionRequest:
      type: object
      properties:
        subscription_id:
          type: string
          format: uuid
        unfreeze_reason:
          type: string
          maxLength: 500
          nullable: true
      required:
        - subscription_id

    # Response Schemas
    ClassesResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
        data:
          type: object
          properties:
            classes:
              type: array
              items:
                $ref: "#/components/schemas/GymClass"
            count:
              type: integer
        errors:
          type: object
          nullable: true

    BookingsResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
        data:
          type: object
          properties:
            bookings:
              type: array
              items:
                $ref: "#/components/schemas/ClassBooking"
            count:
              type: integer
        errors:
          type: object
          nullable: true

    CreateBookingResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
        data:
          type: object
          properties:
            booking:
              $ref: "#/components/schemas/ClassBooking"
            booking_details:
              type: object
              properties:
                class_name:
                  type: string
                session_time:
                  type: string
                  format: date-time
                trainer:
                  type: string
                  nullable: true
                room:
                  type: string
                  nullable: true
        errors:
          type: object
          nullable: true

    SubscriptionPlansResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
        data:
          type: object
          properties:
            plans:
              type: array
              items:
                $ref: "#/components/schemas/SubscriptionPlan"
            count:
              type: integer
        errors:
          type: object
          nullable: true

    SubscriptionPlanDetailsResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
        data:
          $ref: "#/components/schemas/SubscriptionPlan"
        errors:
          type: object
          nullable: true

    SubscriptionsResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
        data:
          type: object
          properties:
            subscriptions:
              type: array
              items:
                $ref: "#/components/schemas/Subscription"
            count:
              type: integer
        errors:
          type: object
          nullable: true

    PurchaseSubscriptionResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
        data:
          type: object
          properties:
            subscription:
              $ref: "#/components/schemas/Subscription"
            payment_details:
              type: object
              properties:
                amount:
                  type: number
                  format: decimal
                payment_method:
                  type: string
                transaction_id:
                  type: string
                  nullable: true
        errors:
          type: object
          nullable: true

    CancelSubscriptionResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
        data:
          type: object
          properties:
            subscription:
              $ref: "#/components/schemas/Subscription"
            cancellation_details:
              type: object
              properties:
                refund_amount:
                  type: number
                  format: decimal
                  nullable: true
                approval_required:
                  type: boolean
                  example: true
        errors:
          type: object
          nullable: true

    FreezeSubscriptionResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
        data:
          type: object
          properties:
            subscription:
              $ref: "#/components/schemas/Subscription"
            freeze_details:
              type: object
              properties:
                new_end_date:
                  type: string
                  format: date-time
                remaining_freeze_credits:
                  type: integer
                remaining_freeze_attempts:
                  type: integer
        errors:
          type: object
          nullable: true

    UnfreezeSubscriptionResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
        data:
          type: object
          properties:
            subscription:
              $ref: "#/components/schemas/Subscription"
        errors:
          type: object
          nullable: true

  responses:
    BadRequest:
      description: Bad request - validation failed or invalid data
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
          example:
            success: false
            message: "Validation failed"
            data: null
            errors:
              field_name: ["error message"]

    Unauthorized:
      description: Unauthorized - invalid credentials or expired token
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
          example:
            success: false
            message: "Authentication failed"
            data: null
            errors: null

    Forbidden:
      description: Forbidden - insufficient permissions or merchant mismatch
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
          example:
            success: false
            message: "Access denied"
            data: null
            errors: null

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
          example:
            success: false
            message: "Resource not found"
            data: null
            errors: null

    Conflict:
      description: Conflict - resource already exists or business rule violation
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
          example:
            success: false
            message: "Resource already exists"
            data: null
            errors: null

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
          example:
            success: false
            message: "Internal server error"
            data: null
            errors: null

tags:
  - name: Customer Authentication
    description: Customer account management and authentication endpoints
  - name: Classes
    description: Gym classes and session management for customers
  - name: Bookings
    description: Class booking management for customers
  - name: Subscription Plans
    description: Available subscription plans for purchase
  - name: Subscriptions
    description: Customer subscription management and operations
