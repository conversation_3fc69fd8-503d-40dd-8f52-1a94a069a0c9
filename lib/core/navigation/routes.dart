import 'package:navigation_utils/navigation_utils.dart';
import 'package:techrar_gym/app/auth/views/auth_gard.dart';
import 'package:techrar_gym/app/auth/views/login_view.dart';
import 'package:techrar_gym/app/auth/views/signup_view.dart';
import 'package:techrar_gym/app/navigation/view/main_scaffold_view.dart';

List<NavigationData> routes = [
  NavigationData(
    label: AuthGard.name,
    url: '/',
    builder: (context, routeData, globalData) => AuthGard(),
  ),
  NavigationData(
    label: LoginView.name,
    url: '/login',
    builder: (context, routeData, globalData) => LoginView(),
  ),
  NavigationData(
    label: SignupView.name,
    url: '/signup',
    builder: (context, routeData, globalData) => SignupView(),
  ),
  NavigationData(
    label: 'main-container-view',
    url: '/home',
    builder: (context, routeData, globalData) => MainScaffold(),
  ),
];
