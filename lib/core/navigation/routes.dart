import 'package:navigation_utils/navigation_utils.dart';
import 'package:techrar_gym/app/auth/views/auth_gard.dart';
import 'package:techrar_gym/app/auth/views/login_view.dart';
import 'package:techrar_gym/app/auth/views/signup_view.dart';
import 'package:techrar_gym/app/navigation/view/main_scaffold_view.dart';
import 'package:techrar_gym/app/subscriptions/views/subscription_plans_view.dart';
import 'package:techrar_gym/app/subscriptions/views/subscription_checkout_view.dart';
import 'package:techrar_gym/app/subscriptions/views/subscription_confirmation_view.dart';

List<NavigationData> routes = [
  NavigationData(
    label: AuthGard.name,
    url: '/',
    builder: (context, routeData, globalData) => AuthGard(),
  ),
  NavigationData(
    label: LoginView.name,
    url: '/login',
    builder: (context, routeData, globalData) => LoginView(),
  ),
  NavigationData(
    label: SignupView.name,
    url: '/signup',
    builder: (context, routeData, globalData) => SignupView(),
  ),
  NavigationData(
    label: 'main-container-view',
    url: '/home',
    builder: (context, routeData, globalData) => MainScaffold(),
  ),
  NavigationData(
    label: SubscriptionPlansView.name,
    url: '/subscription-plans',
    builder: (context, routeData, globalData) => SubscriptionPlansView(),
  ),
  NavigationData(
    label: SubscriptionCheckoutView.name,
    url: '/subscription-checkout',
    builder: (context, routeData, globalData) => SubscriptionCheckoutView(),
  ),
  NavigationData(
    label: SubscriptionConfirmationView.name,
    url: '/subscription-confirmation',
    builder: (context, routeData, globalData) => SubscriptionConfirmationView(),
  ),
];
