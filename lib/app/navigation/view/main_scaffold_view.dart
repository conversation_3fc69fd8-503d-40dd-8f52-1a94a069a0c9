import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:techrar_gym/app/auth/models/session.dart';
import 'package:techrar_gym/core/services/storage_service.dart';
import 'package:techrar_gym/core/theme/app_colors.dart';
import 'package:techrar_gym/core/theme/decorations.dart';
import 'package:techrar_gym/app/classes/view/classes_view.dart';
import 'package:techrar_gym/app/training/view/training_view_simple.dart';
import 'package:techrar_gym/app/profile/view/profile_view.dart';
import 'package:techrar_gym/app/home/<USER>/home_view.dart';

class MainScaffold extends ConsumerStatefulWidget {
  const MainScaffold({super.key});

  @override
  ConsumerState<MainScaffold> createState() => _MainContainerViewState();
}

class _MainContainerViewState extends ConsumerState<MainScaffold> with TickerProviderStateMixin {
  late TabController _tabController;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(() {
      setState(() {
        _currentIndex = _tabController.index;
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: TabBarView(
        controller: _tabController,
        children: const [
          HomeTabView(),
          ClassesView(),
          TrainingView(),
          ProfileView(),
        ],
      ),
      bottomNavigationBar: Container(
        padding: EdgeInsets.only(bottom: MediaQuery.of(context).padding.bottom),
        decoration: BoxDecoration(
          color: AppColors.surfaceColor,
          boxShadow: Styles.boxShadowTop,
        ),
        child: TabBar(
          controller: _tabController,
          indicatorColor: AppColors.primaryColor,
          indicatorWeight: 3.0,
          labelColor: AppColors.primaryColor,
          unselectedLabelColor: AppColors.secondaryTextColor,
          labelStyle: TextStyles.caption.copyWith(
            fontWeight: FontWeight.w600,
            fontSize: 12,
          ),
          unselectedLabelStyle: TextStyles.caption.copyWith(
            fontWeight: FontWeight.w400,
            fontSize: 12,
          ),
          tabs: [
            _buildTab(Icons.home_rounded, 'Home', 0),
            _buildTab(Icons.fitness_center_rounded, 'Classes', 1),
            _buildTab(Icons.sports_gymnastics_rounded, 'Training', 2),
            _buildTab(Icons.person_rounded, 'Profile', 3),
          ],
        ),
      ),
    );
  }

  Widget _buildTab(IconData icon, String label, int index) {
    final isSelected = _currentIndex == index;
    return Tab(
      icon: Icon(
        icon,
        size: AppSizes.iconMd,
        color: isSelected ? AppColors.primaryColor : AppColors.secondaryTextColor,
      ),
      text: label,
    );
  }
}
