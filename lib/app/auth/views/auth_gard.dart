import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:techrar_gym/app/auth/providers/auth_provider.dart';
import 'package:techrar_gym/app/auth/views/login_view.dart';
import 'package:techrar_gym/app/navigation/view/main_scaffold_view.dart';
import 'package:techrar_gym/app/shared/async_value_widget.dart';

class AuthGard extends ConsumerWidget {
  static final name = 'auth-gard';
  const AuthGard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AsyncValueWidget<bool>(
      value: ref.watch(authNotifierProvider),
      data: (p0) => p0 ? const MainScaffold() : const LoginView(),
      // For the first time loading
      loading: () => Material(
        child: Container(
          color: Colors.blue,
        ),
      ),
      skipLoadingOnHasValue: true,
      error: (p0, p1) => Material(
        child: Container(
          color: Colors.red,
        ),
      ),
    );
  }
}
