import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:techrar_gym/core/services/storage_service.dart';
import 'package:techrar_gym/core/theme/app_colors.dart';
import 'package:techrar_gym/core/theme/decorations.dart';
import 'package:techrar_gym/app/home/<USER>/home_provider.dart';
import 'package:techrar_gym/app/home/<USER>/home_header_section.dart';
import 'package:techrar_gym/app/home/<USER>/membership_card.dart';
import 'package:techrar_gym/app/home/<USER>/action_cards_section.dart';
import 'package:techrar_gym/app/home/<USER>/recent_activity_section.dart';

class HomeTabView extends ConsumerWidget {
  const HomeTabView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final homeState = ref.watch(homeNotifierProvider);

    // Debug: Log access token
    StorageService.getAccessToken().then((token) {
      if (token != null) {
        log('Access token: $token');
      }
    });

    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: RefreshIndicator(
        onRefresh: () => ref.read(homeNotifierProvider.notifier).refresh(),
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Section with gradient background
              HomeHeaderSection(
                userProfile: homeState.userProfile,
                todaysGoal: homeState.todaysGoal,
                userStats: homeState.userStats,
                userBadges: homeState.userBadges,
                isLoading: homeState.isLoading,
              ),

              // Content with padding
              Padding(
                padding: const EdgeInsets.all(Insets.l),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Membership Card
                    MembershipCard(
                      membershipStatus: homeState.membershipStatus,
                      isLoading: homeState.isLoading,
                    ),
                    const SizedBox(height: Insets.xl),

                    // Action Cards
                    const ActionCardsSection(),
                    const SizedBox(height: Insets.xl),

                    // Recent Activity
                    RecentActivitySection(
                      recentActivities: homeState.recentActivities,
                      isLoading: homeState.isLoading,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
