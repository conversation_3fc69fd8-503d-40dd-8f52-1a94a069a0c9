import 'package:techrar_gym/app/auth/models/customer.dart';

class HomeState {
  final bool isLoading;
  final String? error;
  final List<QuickAction> quickActions;
  final List<RecentActivity> recentActivities;
  final List<UpcomingClass> upcomingClasses;
  final UserStats? userStats;
  final Customer? userProfile;
  final TodaysGoal? todaysGoal;
  final MembershipStatus? membershipStatus;
  final UserBadges? userBadges;

  const HomeState({
    this.isLoading = false,
    this.error,
    this.quickActions = const [],
    this.recentActivities = const [],
    this.upcomingClasses = const [],
    this.userStats,
    this.userProfile,
    this.todaysGoal,
    this.membershipStatus,
    this.userBadges,
  });

  HomeState copyWith({
    bool? isLoading,
    String? error,
    List<QuickAction>? quickActions,
    List<RecentActivity>? recentActivities,
    List<UpcomingClass>? upcomingClasses,
    UserStats? userStats,
    Customer? userProfile,
    TodaysGoal? todaysGoal,
    MembershipStatus? membershipStatus,
    UserBadges? userBadges,
  }) {
    return HomeState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      quickActions: quickActions ?? this.quickActions,
      recentActivities: recentActivities ?? this.recentActivities,
      upcomingClasses: upcomingClasses ?? this.upcomingClasses,
      userStats: userStats ?? this.userStats,
      userProfile: userProfile ?? this.userProfile,
      todaysGoal: todaysGoal ?? this.todaysGoal,
      membershipStatus: membershipStatus ?? this.membershipStatus,
      userBadges: userBadges ?? this.userBadges,
    );
  }
}

class QuickAction {
  final String title;
  final String icon;
  final String route;

  const QuickAction({
    required this.title,
    required this.icon,
    required this.route,
  });
}

class RecentActivity {
  final String id;
  final String title;
  final String description;
  final DateTime date;
  final String type;

  const RecentActivity({
    required this.id,
    required this.title,
    required this.description,
    required this.date,
    required this.type,
  });
}

class UpcomingClass {
  final String id;
  final String name;
  final String instructor;
  final DateTime startTime;
  final DateTime endTime;
  final String location;
  final int capacity;
  final int enrolled;

  const UpcomingClass({
    required this.id,
    required this.name,
    required this.instructor,
    required this.startTime,
    required this.endTime,
    required this.location,
    required this.capacity,
    required this.enrolled,
  });
}

class UserStats {
  final int totalWorkouts;
  final int totalClasses;
  final int streakDays;
  final double totalHours;

  const UserStats({
    required this.totalWorkouts,
    required this.totalClasses,
    required this.streakDays,
    required this.totalHours,
  });
}

class TodaysGoal {
  final String title;
  final double currentValue;
  final double targetValue;
  final String unit;
  final double percentage;

  const TodaysGoal({
    required this.title,
    required this.currentValue,
    required this.targetValue,
    required this.unit,
    required this.percentage,
  });
}

class MembershipStatus {
  final bool isActive;
  final String? planName;
  final DateTime? expiryDate;
  final String status;

  const MembershipStatus({
    required this.isActive,
    this.planName,
    this.expiryDate,
    required this.status,
  });
}

class UserBadges {
  final int totalBadges;
  final int currentRank;
  final String rankTitle;

  const UserBadges({
    required this.totalBadges,
    required this.currentRank,
    required this.rankTitle,
  });
}
