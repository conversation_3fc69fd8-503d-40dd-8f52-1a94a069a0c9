import 'package:flutter/material.dart';
import 'package:techrar_gym/core/theme/app_colors.dart';
import 'package:techrar_gym/core/theme/decorations.dart';

class ActionCardsSection extends StatelessWidget {
  const ActionCardsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: _buildActionCard(
            context: context,
            icon: Icons.calendar_today,
            title: 'Book Classes',
            subtitle: 'Find your next workout',
            onTap: () {
              // TODO: Navigate to classes
            },
          ),
        ),
        const SizedBox(width: Insets.m),
        Expanded(
          child: _buildActionCard(
            context: context,
            icon: Icons.emoji_events,
            title: 'Challenges',
            subtitle: 'Earn rewards',
            onTap: () {
              // TODO: Navigate to challenges
            },
          ),
        ),
      ],
    );
  }

  Widget _buildActionCard({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(Insets.l),
        decoration: BoxDecoration(
          color: AppColors.cardColor,
          borderRadius: BorderRadius.circular(AppSizes.cardRadius),
          boxShadow: Styles.unifiedShadow,
          border: Border.all(
            color: AppColors.inputBorderColor,
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: AppColors.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppSizes.radiusMd),
              ),
              child: Icon(
                icon,
                color: AppColors.primaryColor,
                size: 24,
              ),
            ),
            const SizedBox(height: Insets.m),
            Text(
              title,
              style: TextStyles.body1b.copyWith(
                color: AppColors.primaryTextColor,
              ),
            ),
            const SizedBox(height: Insets.s),
            Text(
              subtitle,
              style: TextStyles.caption.copyWith(
                color: AppColors.secondaryTextColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
