import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fpdart/fpdart.dart';
import 'package:techrar_gym/app/home/<USER>/home_state.dart';
import 'package:techrar_gym/app/auth/models/customer.dart';
import 'package:techrar_gym/core/services/storage_service.dart';

final homeServiceProvider = Provider<HomeService>(
  (ref) => HomeService(ref),
);

class HomeService {
  final Ref _ref;
  HomeService(this._ref);

  Future<Either<String, List<QuickAction>>> getQuickActions() async {
    try {
      // For now, return static data
      // TODO: Implement API call when backend is ready
      await Future.delayed(const Duration(milliseconds: 500));

      return right([
        const QuickAction(
          title: 'Book Class',
          icon: 'fitness_center',
          route: '/classes',
        ),
        const QuickAction(
          title: 'Start Workout',
          icon: 'play_arrow',
          route: '/training',
        ),
        const QuickAction(
          title: 'View Progress',
          icon: 'trending_up',
          route: '/progress',
        ),
        const QuickAction(
          title: 'Membership',
          icon: 'card_membership',
          route: '/membership',
        ),
      ]);
    } catch (e) {
      return left(e.toString());
    }
  }

  Future<Either<String, List<RecentActivity>>> getRecentActivities() async {
    try {
      // For now, return static data
      // TODO: Implement API call when backend is ready
      await Future.delayed(const Duration(milliseconds: 500));

      return right([
        RecentActivity(
          id: '1',
          title: 'Morning Yoga',
          description: 'Completed 45-minute session',
          date: DateTime.now().subtract(const Duration(hours: 2)),
          type: 'class',
        ),
        RecentActivity(
          id: '2',
          title: 'Strength Training',
          description: 'Upper body workout - 1 hour',
          date: DateTime.now().subtract(const Duration(days: 1)),
          type: 'workout',
        ),
      ]);
    } catch (e) {
      return left(e.toString());
    }
  }

  Future<Either<String, List<UpcomingClass>>> getUpcomingClasses() async {
    try {
      // For now, return static data
      // TODO: Implement API call when backend is ready
      await Future.delayed(const Duration(milliseconds: 500));

      return right([
        UpcomingClass(
          id: '1',
          name: 'HIIT Training',
          instructor: 'Sarah Johnson',
          startTime: DateTime.now().add(const Duration(hours: 2)),
          endTime: DateTime.now().add(const Duration(hours: 3)),
          location: 'Studio A',
          capacity: 20,
          enrolled: 15,
        ),
        UpcomingClass(
          id: '2',
          name: 'Pilates',
          instructor: 'Mike Chen',
          startTime: DateTime.now().add(const Duration(days: 1, hours: 9)),
          endTime: DateTime.now().add(const Duration(days: 1, hours: 10)),
          location: 'Studio B',
          capacity: 15,
          enrolled: 8,
        ),
      ]);
    } catch (e) {
      return left(e.toString());
    }
  }

  Future<Either<String, UserStats>> getUserStats() async {
    try {
      // For now, return static data
      // TODO: Implement API call when backend is ready
      await Future.delayed(const Duration(milliseconds: 500));

      return right(const UserStats(
        totalWorkouts: 45,
        totalClasses: 23,
        streakDays: 7,
        totalHours: 68.5,
      ));
    } catch (e) {
      return left(e.toString());
    }
  }

  Future<Either<String, Customer?>> getUserProfile() async {
    try {
      // Get user profile from storage first, then optionally refresh from API
      final customer = await StorageService.getCustomer();
      return right(customer);
    } catch (e) {
      return left(e.toString());
    }
  }

  Future<Either<String, TodaysGoal>> getTodaysGoal() async {
    try {
      // For now, return static data
      // TODO: Implement API call when backend is ready
      await Future.delayed(const Duration(milliseconds: 500));

      return right(const TodaysGoal(
        title: "Calories",
        currentValue: 0,
        targetValue: 1000,
        unit: "calories",
        percentage: 0.0,
      ));
    } catch (e) {
      return left(e.toString());
    }
  }

  Future<Either<String, MembershipStatus?>> getMembershipStatus() async {
    try {
      // For now, return static data
      // TODO: Implement API call when backend is ready
      await Future.delayed(const Duration(milliseconds: 500));

      return right(null);

      // return right(
      //   MembershipStatus(
      //     isActive: true,
      //     planName: "Premium",
      //     expiryDate: DateTime.now().add(const Duration(days: 30)),
      //     status: "Active",
      //   ),
      // );
    } catch (e) {
      return left(e.toString());
    }
  }

  Future<Either<String, UserBadges>> getUserBadges() async {
    try {
      // For now, return static data
      // TODO: Implement API call when backend is ready
      await Future.delayed(const Duration(milliseconds: 500));

      return right(const UserBadges(
        totalBadges: 12,
        currentRank: 5,
        rankTitle: "Fitness Enthusiast",
      ));
    } catch (e) {
      return left(e.toString());
    }
  }
}
