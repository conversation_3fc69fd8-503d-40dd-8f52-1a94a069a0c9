import 'package:flutter/material.dart';
import 'package:techrar_gym/core/theme/app_colors.dart';
import 'package:techrar_gym/core/theme/decorations.dart';
import 'package:techrar_gym/app/home/<USER>/home_state.dart';
import 'package:techrar_gym/app/auth/models/customer.dart';

class HomeHeaderSection extends StatelessWidget {
  final Customer? userProfile;
  final TodaysGoal? todaysGoal;
  final UserStats? userStats;
  final UserBadges? userBadges;
  final bool isLoading;

  const HomeHeaderSection({
    super.key,
    this.userProfile,
    this.todaysGoal,
    this.userStats,
    this.userBadges,
    required this.isLoading,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primaryColor, AppColors.primaryLightColor],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(AppSizes.cardRadius * 2),
          bottomRight: Radius.circular(AppSizes.cardRadius * 2),
        ),
      ),
      child: Safe<PERSON>rea(
        child: Padding(
          padding: const EdgeInsets.all(Insets.l),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Section 1: User info and notification
              _buildUserSection(),
              const SizedBox(height: Insets.xl),

              // Section 2: Today's Goal Card
              _buildTodaysGoalCard(),
              const SizedBox(height: Insets.l),

              // Section 3: Stats Cards
              _buildStatsCards(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUserSection() {
    return Row(
      children: [
        // User Avatar
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: AppColors.white.withValues(alpha: 0.2),
            border: Border.all(
              color: AppColors.white.withValues(alpha: 0.3),
              width: 2,
            ),
          ),
          child: isLoading
              ? const CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                )
              : userProfile?.profileImageUrl != null
                  ? ClipOval(
                      child: Image.network(
                        userProfile!.profileImageUrl!,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) => _buildDefaultAvatar(),
                      ),
                    )
                  : _buildDefaultAvatar(),
        ),
        const SizedBox(width: Insets.m),

        // Welcome message and name
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Welcome back,',
                style: TextStyles.body2.copyWith(
                  color: AppColors.white.withValues(alpha: 0.9),
                ),
              ),
              const SizedBox(height: 2),
              isLoading
                  ? Container(
                      height: 20,
                      width: 120,
                      decoration: BoxDecoration(
                        color: AppColors.white.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    )
                  : Text(
                      userProfile?.fullName ?? 'User',
                      style: TextStyles.h2.copyWith(
                        color: AppColors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ],
          ),
        ),

        // Notification bell
        IconButton(
          onPressed: () {
            // TODO: Implement notifications
          },
          icon: Icon(
            Icons.notifications_outlined,
            color: AppColors.white,
            size: AppSizes.iconMd,
          ),
        ),
      ],
    );
  }

  Widget _buildDefaultAvatar() {
    return Icon(
      Icons.person,
      color: AppColors.white.withValues(alpha: 0.8),
      size: 24,
    );
  }

  Widget _buildTodaysGoalCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(Insets.l),
      decoration: BoxDecoration(
        color: AppColors.white.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(AppSizes.cardRadius),
        border: Border.all(
          color: AppColors.white.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "Today's Goal",
                style: TextStyles.body1b.copyWith(
                  color: AppColors.white,
                ),
              ),
              isLoading
                  ? Container(
                      height: 16,
                      width: 40,
                      decoration: BoxDecoration(
                        color: AppColors.white.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    )
                  : Text(
                      '${todaysGoal?.percentage.toInt() ?? 0}%',
                      style: TextStyles.body1.copyWith(
                        color: AppColors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ],
          ),
          const SizedBox(height: Insets.m),

          // Progress bar
          Container(
            height: 8,
            decoration: BoxDecoration(
              color: AppColors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(4),
            ),
            child: isLoading
                ? null
                : FractionallySizedBox(
                    alignment: Alignment.centerLeft,
                    widthFactor: (todaysGoal?.percentage ?? 0) / 100,
                    child: Container(
                      decoration: BoxDecoration(
                        color: AppColors.white,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ),
          ),
          const SizedBox(height: Insets.s),

          // Calorie range
          isLoading
              ? Container(
                  height: 14,
                  width: 150,
                  decoration: BoxDecoration(
                    color: AppColors.white.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(4),
                  ),
                )
              : Text(
                  '${todaysGoal?.currentValue.toInt() ?? 0} ${todaysGoal?.unit ?? 'calories'} to ${todaysGoal?.targetValue.toInt() ?? 1000} ${todaysGoal?.unit ?? 'calories'}',
                  style: TextStyles.caption.copyWith(
                    color: AppColors.white.withValues(alpha: 0.8),
                  ),
                ),
        ],
      ),
    );
  }

  Widget _buildStatsCards() {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            icon: Icons.local_fire_department,
            label: 'Day Streak',
            value: isLoading ? null : '${userStats?.streakDays ?? 0}',
          ),
        ),
        const SizedBox(width: Insets.m),
        Expanded(
          child: _buildStatCard(
            icon: Icons.emoji_events,
            label: 'Badges',
            value: isLoading ? null : '${userBadges?.totalBadges ?? 0}',
          ),
        ),
        const SizedBox(width: Insets.m),
        Expanded(
          child: _buildStatCard(
            icon: Icons.people,
            label: 'Rank',
            value: isLoading ? null : '#${userBadges?.currentRank ?? 0}',
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required String label,
    String? value,
  }) {
    return Container(
      padding: const EdgeInsets.all(Insets.m),
      decoration: BoxDecoration(
        color: AppColors.white.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(AppSizes.cardRadius),
        border: Border.all(
          color: AppColors.white.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: AppColors.white,
            size: 24,
          ),
          const SizedBox(height: Insets.s),
          value == null
              ? Container(
                  height: 16,
                  width: 30,
                  decoration: BoxDecoration(
                    color: AppColors.white.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(4),
                  ),
                )
              : Text(
                  value,
                  style: TextStyles.body1.copyWith(
                    color: AppColors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
          const SizedBox(height: 2),
          Text(
            label,
            style: TextStyles.caption.copyWith(
              color: AppColors.white.withValues(alpha: 0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
