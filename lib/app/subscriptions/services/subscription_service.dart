import 'package:techrar_gym/app/subscriptions/models/subscription.dart';
import 'package:techrar_gym/app/subscriptions/models/subscription_plan.dart';
import 'package:techrar_gym/app/subscriptions/models/purchase_subscription_response.dart';

import '../../../core/api/api_response.dart';
import '../../../core/api/api_constants.dart';
import '../../../core/api/api_service.dart';

/// Subscription service that strictly follows the OpenAPI schema
class SubscriptionService {
  /// Get available subscription plans
  /// Endpoint: GET /api/public/subscription-plans
  static Future<ApiResponse<List<SubscriptionPlan>>> getSubscriptionPlans({
    String? branchId,
    bool? isActive,
  }) async {
    final queryParams = <String, String>{};
    if (branchId != null) queryParams['branch_id'] = branchId;
    if (isActive != null) queryParams['is_active'] = isActive.toString();

    return await ApiService.get<List<SubscriptionPlan>>(
      ApiConstants.subscriptionPlans,
      queryParams: queryParams,
      includeAuth: false,
      fromJsonT: (data) {
        final plansData = data['data']['plans'] as List;
        return plansData.map((plan) => SubscriptionPlan.fromJson(plan)).toList();
      },
    );
  }

  /// Get subscription plan details
  /// Endpoint: GET /api/public/subscription-plans/{id}
  static Future<ApiResponse<SubscriptionPlan>> getSubscriptionPlanDetails(String planId) async {
    return await ApiService.get<SubscriptionPlan>(
      '${ApiConstants.subscriptionPlans}/$planId',
      includeAuth: false,
      fromJsonT: (data) => SubscriptionPlan.fromJson(data['data']),
    );
  }

  /// Purchase subscription plan
  /// Endpoint: POST /api/public/subscriptions/purchase
  static Future<ApiResponse<PurchaseSubscriptionResponse>> purchaseSubscription({
    required String planId,
    String? paymentMethod,
    bool autoRenew = true,
    String? startDate,
    String? promoCode,
    String? notes,
  }) async {
    final body = <String, dynamic>{
      'plan_id': planId,
      'auto_renew': autoRenew,
    };

    if (paymentMethod != null) body['payment_method'] = paymentMethod;
    if (startDate != null) body['start_date'] = startDate;
    if (promoCode != null) body['promo_code'] = promoCode;
    if (notes != null) body['notes'] = notes;

    return await ApiService.post<PurchaseSubscriptionResponse>(
      ApiConstants.purchaseSubscription,
      body: body,
      fromJsonT: (data) => PurchaseSubscriptionResponse.fromJson(data),
    );
  }

  /// Get customer subscriptions
  /// Endpoint: GET /api/public/subscriptions
  static Future<ApiResponse<List<Subscription>>> getCustomerSubscriptions({
    String? status,
  }) async {
    final queryParams = <String, String>{};
    if (status != null) queryParams['status'] = status;

    return await ApiService.get<List<Subscription>>(
      ApiConstants.subscriptions,
      queryParams: queryParams,
      fromJsonT: (data) {
        final subscriptionsData = data['data']['subscriptions'] as List;
        return subscriptionsData.map((subscription) => Subscription.fromJson(subscription)).toList();
      },
    );
  }

  static Future<ApiResponse<Map<String, dynamic>>> freezeSubscription({
    required String reason,
    required int days,
    String? note,
  }) async {
    final body = {
      'reason': reason,
      'days': days,
    };

    if (note != null) body['note'] = note;

    return await ApiService.post<Map<String, dynamic>>(
      ApiConstants.freezeSubscription,
      body: body,
      fromJsonT: (data) => data as Map<String, dynamic>,
    );
  }

  static Future<ApiResponse<Map<String, dynamic>>> unfreezeSubscription() async {
    return await ApiService.post<Map<String, dynamic>>(
      '${ApiConstants.subscriptions}/unfreeze',
      fromJsonT: (data) => data as Map<String, dynamic>,
    );
  }

  static Future<ApiResponse<Map<String, dynamic>>> cancelSubscription({
    required String reason,
    String? feedback,
    bool immediate = false,
  }) async {
    final body = {
      'reason': reason,
      'immediate': immediate,
    };

    if (feedback != null) body['feedback'] = feedback;

    return await ApiService.post<Map<String, dynamic>>(
      ApiConstants.cancelSubscription,
      body: body,
      fromJsonT: (data) => data as Map<String, dynamic>,
    );
  }
}
