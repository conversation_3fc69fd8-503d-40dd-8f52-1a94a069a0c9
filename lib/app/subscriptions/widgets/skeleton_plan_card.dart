import 'package:flutter/material.dart';
import 'package:techrar_gym/core/theme/app_colors.dart';
import 'package:techrar_gym/core/theme/decorations.dart';

/// Skeleton loader for subscription plan cards
class SkeletonPlanCard extends StatefulWidget {
  const SkeletonPlanCard({super.key});

  @override
  State<SkeletonPlanCard> createState() => _SkeletonPlanCardState();
}

class _SkeletonPlanCardState extends State<SkeletonPlanCard> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.3, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          padding: const EdgeInsets.all(Insets.l),
          decoration: BoxDecoration(
            color: AppColors.cardColor,
            borderRadius: BorderRadius.circular(AppSizes.cardRadius),
            boxShadow: Styles.unifiedShadow,
            border: Border.all(
              color: AppColors.inputBorderColor,
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Plan name skeleton
              Container(
                height: 24,
                width: 200,
                decoration: BoxDecoration(
                  color: AppColors.inputBorderColor.withValues(alpha: _animation.value),
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              const SizedBox(height: Insets.m),

              // Price skeleton
              Container(
                height: 32,
                width: 120,
                decoration: BoxDecoration(
                  color: AppColors.inputBorderColor.withValues(alpha: _animation.value),
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              const SizedBox(height: Insets.s),

              // Duration skeleton
              Container(
                height: 16,
                width: 80,
                decoration: BoxDecoration(
                  color: AppColors.inputBorderColor.withValues(alpha: _animation.value),
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              const SizedBox(height: Insets.l),

              // Features skeleton
              ...List.generate(
                  3,
                  (index) => Padding(
                        padding: const EdgeInsets.only(bottom: Insets.s),
                        child: Row(
                          children: [
                            Container(
                              height: 16,
                              width: 16,
                              decoration: BoxDecoration(
                                color: AppColors.inputBorderColor.withValues(alpha: _animation.value),
                                borderRadius: BorderRadius.circular(2),
                              ),
                            ),
                            const SizedBox(width: Insets.s),
                            Container(
                              height: 16,
                              width: 150 + (index * 20).toDouble(),
                              decoration: BoxDecoration(
                                color: AppColors.inputBorderColor.withValues(alpha: _animation.value),
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ),
                          ],
                        ),
                      )),
              const SizedBox(height: Insets.l),

              // Button skeleton
              Container(
                height: 48,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: AppColors.inputBorderColor.withValues(alpha: _animation.value),
                  borderRadius: BorderRadius.circular(AppSizes.radiusMd),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
