import 'package:flutter/material.dart';
import 'package:techrar_gym/core/theme/app_colors.dart';
import 'package:techrar_gym/core/theme/decorations.dart';
import 'package:techrar_gym/app/subscriptions/models/subscription_plan.dart';

/// Widget to display checkout summary
class CheckoutSummaryCard extends StatelessWidget {
  final SubscriptionPlan plan;
  final bool autoRenew;
  final String? promoCode;
  final double totalPrice;

  const CheckoutSummaryCard({
    super.key,
    required this.plan,
    required this.autoRenew,
    this.promoCode,
    required this.totalPrice,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(Insets.l),
      decoration: BoxDecoration(
        color: AppColors.cardColor,
        borderRadius: BorderRadius.circular(AppSizes.cardRadius),
        boxShadow: Styles.unifiedShadow,
        border: Border.all(
          color: AppColors.inputBorderColor,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'Order Summary',
            style: TextStyles.h3.copyWith(
              color: AppColors.primaryTextColor,
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(height: Insets.l),
          
          // Plan details
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      plan.name,
                      style: TextStyles.body1b.copyWith(
                        color: AppColors.primaryTextColor,
                      ),
                    ),
                    if (plan.nameAlt != null && plan.nameAlt!.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 2),
                        child: Text(
                          plan.nameAlt!,
                          style: TextStyles.caption.copyWith(
                            color: AppColors.secondaryTextColor,
                          ),
                        ),
                      ),
                    const SizedBox(height: Insets.s),
                    Text(
                      plan.durationText,
                      style: TextStyles.body2.copyWith(
                        color: AppColors.secondaryTextColor,
                      ),
                    ),
                    if (plan.classesPerWeekText != null)
                      Text(
                        plan.classesPerWeekText!,
                        style: TextStyles.body2.copyWith(
                          color: AppColors.secondaryTextColor,
                        ),
                      ),
                  ],
                ),
              ),
              Text(
                '\$${plan.price.toStringAsFixed(2)}',
                style: TextStyles.h3.copyWith(
                  color: AppColors.primaryColor,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: Insets.l),
          Divider(color: AppColors.inputBorderColor),
          const SizedBox(height: Insets.l),
          
          // Auto-renewal info
          Row(
            children: [
              Icon(
                autoRenew ? Icons.autorenew : Icons.event_repeat,
                size: 20,
                color: autoRenew ? AppColors.successColor : AppColors.secondaryTextColor,
              ),
              const SizedBox(width: Insets.m),
              Expanded(
                child: Text(
                  autoRenew 
                      ? 'Auto-renewal enabled'
                      : 'Auto-renewal disabled',
                  style: TextStyles.body2.copyWith(
                    color: AppColors.primaryTextColor,
                  ),
                ),
              ),
            ],
          ),
          
          if (autoRenew) ...[
            const SizedBox(height: Insets.s),
            Padding(
              padding: const EdgeInsets.only(left: 32),
              child: Text(
                'Your subscription will automatically renew at the end of each billing period',
                style: TextStyles.caption.copyWith(
                  color: AppColors.secondaryTextColor,
                ),
              ),
            ),
          ],
          
          // Promo code (if applied)
          if (promoCode != null && promoCode!.isNotEmpty) ...[
            const SizedBox(height: Insets.l),
            Container(
              padding: const EdgeInsets.all(Insets.m),
              decoration: BoxDecoration(
                color: AppColors.successColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppSizes.radiusSm),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.local_offer,
                    size: 20,
                    color: AppColors.successColor,
                  ),
                  const SizedBox(width: Insets.m),
                  Expanded(
                    child: Text(
                      'Promo code applied: $promoCode',
                      style: TextStyles.body2.copyWith(
                        color: AppColors.successColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
          
          const SizedBox(height: Insets.l),
          Divider(color: AppColors.inputBorderColor),
          const SizedBox(height: Insets.l),
          
          // Total
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total',
                style: TextStyles.h3.copyWith(
                  color: AppColors.primaryTextColor,
                  fontWeight: FontWeight.w700,
                ),
              ),
              Text(
                '\$${totalPrice.toStringAsFixed(2)}',
                style: TextStyles.h2.copyWith(
                  color: AppColors.primaryColor,
                  fontWeight: FontWeight.w800,
                ),
              ),
            ],
          ),
          
          // Freeze credits info
          if (plan.freezeDaysCredit > 0 || plan.freezeAttemptsCredit > 0) ...[
            const SizedBox(height: Insets.l),
            Container(
              padding: const EdgeInsets.all(Insets.m),
              decoration: BoxDecoration(
                color: AppColors.infoColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppSizes.radiusSm),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.ac_unit,
                        size: 16,
                        color: AppColors.infoColor,
                      ),
                      const SizedBox(width: Insets.s),
                      Text(
                        'Freeze Benefits Included',
                        style: TextStyles.caption.copyWith(
                          color: AppColors.infoColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: Insets.s),
                  Text(
                    '• ${plan.freezeDaysCredit} freeze days available\n• ${plan.freezeAttemptsCredit} freeze attempts allowed',
                    style: TextStyles.caption.copyWith(
                      color: AppColors.infoColor,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}
