import 'package:flutter/material.dart';
import 'package:techrar_gym/core/theme/app_colors.dart';
import 'package:techrar_gym/core/theme/decorations.dart';
import 'package:techrar_gym/app/subscriptions/models/subscription_plan.dart';
import 'package:techrar_gym/app/subscriptions/widgets/plan_feature_item.dart';

/// Widget to display a subscription plan card
class SubscriptionPlanCard extends StatelessWidget {
  final SubscriptionPlan plan;
  final bool isSelected;
  final VoidCallback? onTap;
  final bool showFeatures;

  const SubscriptionPlanCard({
    super.key,
    required this.plan,
    this.isSelected = false,
    this.onTap,
    this.showFeatures = true,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.all(Insets.l),
        decoration: BoxDecoration(
          color: AppColors.cardColor,
          borderRadius: BorderRadius.circular(AppSizes.cardRadius),
          boxShadow: isSelected ? Styles.elevatedShadow : Styles.unifiedShadow,
          border: Border.all(
            color: isSelected 
                ? AppColors.primaryColor
                : AppColors.inputBorderColor,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with plan name and selection indicator
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    plan.name,
                    style: TextStyles.h3.copyWith(
                      color: AppColors.primaryTextColor,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
                if (isSelected)
                  Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: AppColors.primaryColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.check,
                      size: 16,
                      color: AppColors.white,
                    ),
                  ),
              ],
            ),
            
            if (plan.nameAlt != null && plan.nameAlt!.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: Insets.s),
                child: Text(
                  plan.nameAlt!,
                  style: TextStyles.body2.copyWith(
                    color: AppColors.secondaryTextColor,
                  ),
                ),
              ),
            
            const SizedBox(height: Insets.m),
            
            // Price and duration
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '\$${plan.price.toStringAsFixed(0)}',
                  style: TextStyles.h1.copyWith(
                    color: AppColors.primaryColor,
                    fontWeight: FontWeight.w800,
                  ),
                ),
                const SizedBox(width: Insets.s),
                Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Text(
                    '/ ${plan.durationText}',
                    style: TextStyles.body2.copyWith(
                      color: AppColors.secondaryTextColor,
                    ),
                  ),
                ),
              ],
            ),
            
            // Classes per week (if available)
            if (plan.classesPerWeekText != null)
              Padding(
                padding: const EdgeInsets.only(top: Insets.s),
                child: Text(
                  plan.classesPerWeekText!,
                  style: TextStyles.caption.copyWith(
                    color: AppColors.secondaryTextColor,
                  ),
                ),
              ),
            
            // Description (if available)
            if (plan.description != null && plan.description!.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: Insets.m),
                child: Text(
                  plan.description!,
                  style: TextStyles.body2.copyWith(
                    color: AppColors.secondaryTextColor,
                  ),
                ),
              ),
            
            // Features
            if (showFeatures && plan.features.isNotEmpty) ...[
              const SizedBox(height: Insets.l),
              Text(
                'Features',
                style: TextStyles.body1b.copyWith(
                  color: AppColors.primaryTextColor,
                ),
              ),
              const SizedBox(height: Insets.m),
              ...plan.features.take(5).map((feature) => PlanFeatureItem(
                feature: feature,
                isCompact: true,
              )),
              if (plan.features.length > 5)
                Padding(
                  padding: const EdgeInsets.only(top: Insets.s),
                  child: Text(
                    '+${plan.features.length - 5} more features',
                    style: TextStyles.caption.copyWith(
                      color: AppColors.primaryColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
            ],
            
            // Freeze credits info
            if (plan.freezeDaysCredit > 0 || plan.freezeAttemptsCredit > 0) ...[
              const SizedBox(height: Insets.m),
              Container(
                padding: const EdgeInsets.all(Insets.m),
                decoration: BoxDecoration(
                  color: AppColors.infoColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppSizes.radiusSm),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.ac_unit,
                      size: 16,
                      color: AppColors.infoColor,
                    ),
                    const SizedBox(width: Insets.s),
                    Expanded(
                      child: Text(
                        '${plan.freezeDaysCredit} freeze days, ${plan.freezeAttemptsCredit} attempts',
                        style: TextStyles.caption.copyWith(
                          color: AppColors.infoColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
            
            const SizedBox(height: Insets.l),
            
            // Select button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: onTap,
                style: ElevatedButton.styleFrom(
                  backgroundColor: isSelected 
                      ? AppColors.primaryColor
                      : AppColors.buttonSecondaryColor,
                  foregroundColor: isSelected 
                      ? AppColors.buttonTextColor
                      : AppColors.buttonSecondaryTextColor,
                  padding: const EdgeInsets.symmetric(vertical: Insets.m),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppSizes.radiusMd),
                  ),
                  elevation: 0,
                ),
                child: Text(
                  isSelected ? 'Selected' : 'Select Plan',
                  style: TextStyles.buttonText.copyWith(
                    color: isSelected 
                        ? AppColors.buttonTextColor
                        : AppColors.buttonSecondaryTextColor,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
