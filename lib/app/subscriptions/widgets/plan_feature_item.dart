import 'package:flutter/material.dart';
import 'package:techrar_gym/core/theme/app_colors.dart';
import 'package:techrar_gym/core/theme/decorations.dart';
import 'package:techrar_gym/app/subscriptions/models/subscription_plan_feature.dart';

/// Widget to display a subscription plan feature
class PlanFeatureItem extends StatelessWidget {
  final SubscriptionPlanFeature feature;
  final bool isCompact;

  const PlanFeatureItem({
    super.key,
    required this.feature,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: isCompact ? Insets.s : Insets.m),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Feature icon or check mark
          Container(
            width: 20,
            height: 20,
            margin: const EdgeInsets.only(top: 2),
            decoration: BoxDecoration(
              color: feature.isHighlighted 
                  ? AppColors.primaryColor.withValues(alpha: 0.1)
                  : AppColors.successColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              feature.icon != null ? _getIconFromString(feature.icon!) : Icons.check,
              size: 12,
              color: feature.isHighlighted 
                  ? AppColors.primaryColor
                  : AppColors.successColor,
            ),
          ),
          const SizedBox(width: Insets.m),
          
          // Feature text
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  feature.displayText,
                  style: isCompact 
                      ? TextStyles.caption.copyWith(
                          color: AppColors.primaryTextColor,
                          fontWeight: feature.isHighlighted ? FontWeight.w600 : FontWeight.w500,
                        )
                      : TextStyles.body2.copyWith(
                          color: AppColors.primaryTextColor,
                          fontWeight: feature.isHighlighted ? FontWeight.w600 : FontWeight.w500,
                        ),
                ),
                if (feature.value.isNotEmpty && feature.value != feature.displayText)
                  Padding(
                    padding: const EdgeInsets.only(top: 2),
                    child: Text(
                      feature.value,
                      style: TextStyles.caption.copyWith(
                        color: AppColors.secondaryTextColor,
                      ),
                    ),
                  ),
              ],
            ),
          ),
          
          // Premium badge
          if (feature.isHighlighted)
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: Insets.s,
                vertical: 2,
              ),
              decoration: BoxDecoration(
                color: AppColors.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppSizes.radiusSm),
              ),
              child: Text(
                'Premium',
                style: TextStyles.caption.copyWith(
                  color: AppColors.primaryColor,
                  fontWeight: FontWeight.w600,
                  fontSize: 10,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Convert string icon name to IconData
  IconData _getIconFromString(String iconName) {
    switch (iconName.toLowerCase()) {
      case 'fitness_center':
      case 'gym':
        return Icons.fitness_center;
      case 'pool':
      case 'swimming':
        return Icons.pool;
      case 'spa':
      case 'sauna':
        return Icons.spa;
      case 'group':
      case 'people':
        return Icons.group;
      case 'schedule':
      case 'time':
        return Icons.schedule;
      case 'lock_open':
      case 'access':
        return Icons.lock_open;
      case 'local_parking':
      case 'parking':
        return Icons.local_parking;
      case 'wifi':
        return Icons.wifi;
      case 'restaurant':
      case 'food':
        return Icons.restaurant;
      case 'ac_unit':
      case 'cooling':
        return Icons.ac_unit;
      case 'security':
        return Icons.security;
      case 'support_agent':
      case 'support':
        return Icons.support_agent;
      default:
        return Icons.check;
    }
  }
}
