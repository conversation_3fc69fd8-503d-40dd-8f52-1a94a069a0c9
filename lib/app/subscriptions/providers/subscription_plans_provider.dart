import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:techrar_gym/app/subscriptions/models/subscription_plan.dart';
import 'package:techrar_gym/app/subscriptions/services/subscription_service.dart';

/// State for subscription plans
class SubscriptionPlansState {
  final List<SubscriptionPlan> plans;
  final bool isLoading;
  final String? error;

  SubscriptionPlansState({
    this.plans = const [],
    this.isLoading = false,
    this.error,
  });

  SubscriptionPlansState copyWith({
    List<SubscriptionPlan>? plans,
    bool? isLoading,
    String? error,
  }) {
    return SubscriptionPlansState(
      plans: plans ?? this.plans,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

/// Subscription plans notifier
class SubscriptionPlansNotifier extends StateNotifier<SubscriptionPlansState> {
  SubscriptionPlansNotifier() : super(SubscriptionPlansState());

  /// Load subscription plans
  Future<void> loadPlans({String? branchId}) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await SubscriptionService.getSubscriptionPlans(
        branchId: branchId,
        isActive: true,
      );

      if (response.success && response.data != null) {
        state = state.copyWith(
          plans: response.data!,
          isLoading: false,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.message.isNotEmpty ? response.message : 'Failed to load subscription plans',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'An error occurred while loading subscription plans',
      );
    }
  }

  /// Refresh plans
  Future<void> refresh({String? branchId}) async {
    await loadPlans(branchId: branchId);
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// Provider for subscription plans
final subscriptionPlansProvider = StateNotifierProvider<SubscriptionPlansNotifier, SubscriptionPlansState>((ref) {
  return SubscriptionPlansNotifier();
});

/// Provider for a specific subscription plan
final subscriptionPlanProvider = FutureProvider.family<SubscriptionPlan?, String>((ref, planId) async {
  try {
    final response = await SubscriptionService.getSubscriptionPlanDetails(planId);
    return response.success ? response.data : null;
  } catch (e) {
    return null;
  }
});
