import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:techrar_gym/app/subscriptions/models/subscription_plan.dart';
import 'package:techrar_gym/app/subscriptions/models/purchase_subscription_response.dart';
import 'package:techrar_gym/app/subscriptions/services/subscription_service.dart';

/// State for subscription checkout
class SubscriptionCheckoutState {
  final SubscriptionPlan? selectedPlan;
  final bool autoRenew;
  final String? promoCode;
  final String? notes;
  final bool isLoading;
  final String? error;
  final PurchaseSubscriptionResponse? purchaseResult;

  SubscriptionCheckoutState({
    this.selectedPlan,
    this.autoRenew = true,
    this.promoCode,
    this.notes,
    this.isLoading = false,
    this.error,
    this.purchaseResult,
  });

  SubscriptionCheckoutState copyWith({
    SubscriptionPlan? selectedPlan,
    bool? autoRenew,
    String? promoCode,
    String? notes,
    bool? isLoading,
    String? error,
    PurchaseSubscriptionResponse? purchaseResult,
  }) {
    return SubscriptionCheckoutState(
      selectedPlan: selectedPlan ?? this.selectedPlan,
      autoRenew: autoRenew ?? this.autoRenew,
      promoCode: promoCode ?? this.promoCode,
      notes: notes ?? this.notes,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      purchaseResult: purchaseResult ?? this.purchaseResult,
    );
  }

  /// Calculate total price (could include promo code discounts in the future)
  double get totalPrice => selectedPlan?.price ?? 0.0;

  /// Check if checkout is ready
  bool get isReadyForCheckout => selectedPlan != null && !isLoading;
}

/// Subscription checkout notifier
class SubscriptionCheckoutNotifier extends StateNotifier<SubscriptionCheckoutState> {
  SubscriptionCheckoutNotifier() : super(SubscriptionCheckoutState());

  /// Set selected plan
  void setSelectedPlan(SubscriptionPlan plan) {
    state = state.copyWith(selectedPlan: plan, error: null);
  }

  /// Toggle auto-renewal
  void setAutoRenew(bool autoRenew) {
    state = state.copyWith(autoRenew: autoRenew);
  }

  /// Set promo code
  void setPromoCode(String? promoCode) {
    state = state.copyWith(promoCode: promoCode);
  }

  /// Set notes
  void setNotes(String? notes) {
    state = state.copyWith(notes: notes);
  }

  /// Purchase subscription
  Future<bool> purchaseSubscription() async {
    if (state.selectedPlan == null) {
      state = state.copyWith(error: 'No plan selected');
      return false;
    }

    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await SubscriptionService.purchaseSubscription(
        planId: state.selectedPlan!.id,
        autoRenew: state.autoRenew,
        promoCode: state.promoCode?.isNotEmpty == true ? state.promoCode : null,
        notes: state.notes?.isNotEmpty == true ? state.notes : null,
      );

      if (response.success && response.data != null) {
        state = state.copyWith(
          isLoading: false,
          purchaseResult: response.data,
        );
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.message.isNotEmpty ? response.message : 'Failed to purchase subscription',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'An error occurred during purchase',
      );
      return false;
    }
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Reset checkout state
  void reset() {
    state = SubscriptionCheckoutState();
  }
}

/// Provider for subscription checkout
final subscriptionCheckoutProvider =
    StateNotifierProvider<SubscriptionCheckoutNotifier, SubscriptionCheckoutState>((ref) {
  return SubscriptionCheckoutNotifier();
});
