import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:navigation_utils/navigation_utils.dart';
import 'package:techrar_gym/core/theme/app_colors.dart';
import 'package:techrar_gym/core/theme/decorations.dart';
import 'package:techrar_gym/app/subscriptions/providers/subscription_plans_provider.dart';
import 'package:techrar_gym/app/subscriptions/providers/subscription_checkout_provider.dart';
import 'package:techrar_gym/app/subscriptions/widgets/subscription_plan_card.dart';
import 'package:techrar_gym/app/subscriptions/widgets/skeleton_plan_card.dart';
import 'package:techrar_gym/app/subscriptions/views/subscription_checkout_view.dart';

/// View to display available subscription plans
class SubscriptionPlansView extends ConsumerStatefulWidget {
  static const String name = 'subscription-plans-view';

  const SubscriptionPlansView({super.key});

  @override
  ConsumerState<SubscriptionPlansView> createState() => _SubscriptionPlansViewState();
}

class _SubscriptionPlansViewState extends ConsumerState<SubscriptionPlansView> {
  @override
  void initState() {
    super.initState();
    // Load plans when the view is initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(subscriptionPlansProvider.notifier).loadPlans();
    });
  }

  @override
  Widget build(BuildContext context) {
    final plansState = ref.watch(subscriptionPlansProvider);
    final checkoutNotifier = ref.read(subscriptionCheckoutProvider.notifier);

    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppColors.surfaceColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: AppColors.primaryTextColor,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          'Choose Your Plan',
          style: TextStyles.h2.copyWith(
            color: AppColors.primaryTextColor,
            fontWeight: FontWeight.w700,
          ),
        ),
        centerTitle: true,
      ),
      body: RefreshIndicator(
        onRefresh: () => ref.read(subscriptionPlansProvider.notifier).refresh(),
        child: _buildBody(plansState, checkoutNotifier),
      ),
    );
  }

  Widget _buildBody(SubscriptionPlansState plansState, SubscriptionCheckoutNotifier checkoutNotifier) {
    if (plansState.isLoading && plansState.plans.isEmpty) {
      return _buildLoadingState();
    }

    if (plansState.error != null && plansState.plans.isEmpty) {
      return _buildErrorState(plansState.error!);
    }

    if (plansState.plans.isEmpty) {
      return _buildEmptyState();
    }

    return _buildPlansGrid(plansState.plans, checkoutNotifier);
  }

  Widget _buildLoadingState() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(Insets.l),
      child: Column(
        children: [
          _buildHeader(),
          const SizedBox(height: Insets.xl),
          ...List.generate(
              3,
              (index) => Padding(
                    padding: const EdgeInsets.only(bottom: Insets.l),
                    child: const SkeletonPlanCard(),
                  )),
        ],
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(Insets.l),
      child: Column(
        children: [
          _buildHeader(),
          const SizedBox(height: Insets.xl),
          Container(
            padding: const EdgeInsets.all(Insets.xl),
            decoration: BoxDecoration(
              color: AppColors.cardColor,
              borderRadius: BorderRadius.circular(AppSizes.cardRadius),
              boxShadow: Styles.unifiedShadow,
            ),
            child: Column(
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: AppColors.errorColor,
                ),
                const SizedBox(height: Insets.l),
                Text(
                  'Oops! Something went wrong',
                  style: TextStyles.h2.copyWith(
                    color: AppColors.primaryTextColor,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: Insets.m),
                Text(
                  error,
                  style: TextStyles.body2.copyWith(
                    color: AppColors.secondaryTextColor,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: Insets.l),
                ElevatedButton(
                  onPressed: () => ref.read(subscriptionPlansProvider.notifier).refresh(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryColor,
                    foregroundColor: AppColors.buttonTextColor,
                  ),
                  child: Text('Try Again', style: TextStyles.buttonText),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(Insets.l),
      child: Column(
        children: [
          _buildHeader(),
          const SizedBox(height: Insets.xl),
          Container(
            padding: const EdgeInsets.all(Insets.xl),
            decoration: BoxDecoration(
              color: AppColors.cardColor,
              borderRadius: BorderRadius.circular(AppSizes.cardRadius),
              boxShadow: Styles.unifiedShadow,
            ),
            child: Column(
              children: [
                Icon(
                  Icons.inbox_outlined,
                  size: 64,
                  color: AppColors.secondaryTextColor,
                ),
                const SizedBox(height: Insets.l),
                Text(
                  'No Plans Available',
                  style: TextStyles.h2.copyWith(
                    color: AppColors.primaryTextColor,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: Insets.m),
                Text(
                  'There are no subscription plans available at the moment. Please check back later.',
                  style: TextStyles.body2.copyWith(
                    color: AppColors.secondaryTextColor,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlansGrid(List plans, SubscriptionCheckoutNotifier checkoutNotifier) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(Insets.l),
      child: Column(
        children: [
          _buildHeader(),
          const SizedBox(height: Insets.xl),
          ...plans.map((plan) => Padding(
                padding: const EdgeInsets.only(bottom: Insets.l),
                child: SubscriptionPlanCard(
                  plan: plan,
                  onTap: () {
                    checkoutNotifier.setSelectedPlan(plan);
                    NavigationManager.instance.push(SubscriptionCheckoutView.name);
                  },
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Text(
          'Select the perfect plan for your fitness journey',
          style: TextStyles.body1.copyWith(
            color: AppColors.secondaryTextColor,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: Insets.m),
        Container(
          padding: const EdgeInsets.all(Insets.m),
          decoration: BoxDecoration(
            color: AppColors.infoColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppSizes.radiusMd),
          ),
          child: Row(
            children: [
              Icon(
                Icons.info_outline,
                size: 20,
                color: AppColors.infoColor,
              ),
              const SizedBox(width: Insets.m),
              Expanded(
                child: Text(
                  'All plans include access to gym equipment and basic facilities',
                  style: TextStyles.caption.copyWith(
                    color: AppColors.infoColor,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
