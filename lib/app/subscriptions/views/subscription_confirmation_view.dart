import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:techrar_gym/core/theme/app_colors.dart';
import 'package:techrar_gym/core/theme/decorations.dart';
import 'package:techrar_gym/app/subscriptions/providers/subscription_checkout_provider.dart';
import 'package:techrar_gym/app/home/<USER>/home_provider.dart';

/// View for subscription confirmation
class SubscriptionConfirmationView extends ConsumerWidget {
  static const String name = 'subscription-confirmation-view';

  const SubscriptionConfirmationView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final checkoutState = ref.watch(subscriptionCheckoutProvider);
    final purchaseResult = checkoutState.purchaseResult;

    // If no purchase result, redirect to plans
    if (purchaseResult == null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Navigator.of(context).pop();
      });
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(Insets.l),
          child: Column(
            children: [
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Success animation/icon
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        color: AppColors.successColor.withValues(alpha: 0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.check_circle,
                        size: 80,
                        color: AppColors.successColor,
                      ),
                    ),

                    const SizedBox(height: Insets.xl),

                    // Success message
                    Text(
                      'Subscription Activated!',
                      style: TextStyles.h1.copyWith(
                        color: AppColors.primaryTextColor,
                        fontWeight: FontWeight.w800,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: Insets.m),

                    Text(
                      'Welcome to your new fitness journey! Your subscription is now active.',
                      style: TextStyles.body1.copyWith(
                        color: AppColors.secondaryTextColor,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: Insets.xl),

                    // Subscription details card
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(Insets.l),
                      decoration: BoxDecoration(
                        color: AppColors.cardColor,
                        borderRadius: BorderRadius.circular(AppSizes.cardRadius),
                        boxShadow: Styles.unifiedShadow,
                        border: Border.all(
                          color: AppColors.successColor.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: Column(
                        children: [
                          Text(
                            'Subscription Details',
                            style: TextStyles.h2.copyWith(
                              color: AppColors.primaryTextColor,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                          const SizedBox(height: Insets.l),
                          _buildDetailRow(
                            'Plan',
                            purchaseResult.data.subscription.plan.name,
                          ),
                          _buildDetailRow(
                            'Duration',
                            purchaseResult.data.subscription.plan.durationText,
                          ),
                          _buildDetailRow(
                            'Amount Paid',
                            '\$${purchaseResult.data.paymentDetails.amount.toStringAsFixed(2)}',
                          ),
                          _buildDetailRow(
                            'Start Date',
                            _formatDate(purchaseResult.data.subscription.startDate),
                          ),
                          _buildDetailRow(
                            'End Date',
                            _formatDate(purchaseResult.data.subscription.endDate),
                          ),
                          if (purchaseResult.data.subscription.autoRenew)
                            _buildDetailRow(
                              'Auto-Renewal',
                              'Enabled',
                              valueColor: AppColors.successColor,
                            ),
                          if (purchaseResult.data.paymentDetails.transactionId != null)
                            _buildDetailRow(
                              'Transaction ID',
                              purchaseResult.data.paymentDetails.transactionId!,
                            ),
                        ],
                      ),
                    ),

                    const SizedBox(height: Insets.xl),

                    // Benefits info
                    Container(
                      padding: const EdgeInsets.all(Insets.l),
                      decoration: BoxDecoration(
                        color: AppColors.primaryColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(AppSizes.radiusMd),
                      ),
                      child: Column(
                        children: [
                          Icon(
                            Icons.emoji_events,
                            size: 32,
                            color: AppColors.primaryColor,
                          ),
                          const SizedBox(height: Insets.m),
                          Text(
                            'You now have access to all gym facilities and can start booking classes immediately!',
                            style: TextStyles.body2.copyWith(
                              color: AppColors.primaryColor,
                              fontWeight: FontWeight.w600,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // Action buttons
              Column(
                children: [
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () => _goToHome(context, ref),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primaryColor,
                        foregroundColor: AppColors.buttonTextColor,
                        padding: const EdgeInsets.symmetric(vertical: Insets.l),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(AppSizes.radiusMd),
                        ),
                        elevation: 0,
                      ),
                      child: Text(
                        'Go to Home',
                        style: TextStyles.buttonText.copyWith(
                          fontSize: 16,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: Insets.m),
                  SizedBox(
                    width: double.infinity,
                    child: OutlinedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppColors.primaryColor,
                        side: BorderSide(color: AppColors.primaryColor),
                        padding: const EdgeInsets.symmetric(vertical: Insets.l),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(AppSizes.radiusMd),
                        ),
                      ),
                      child: Text(
                        'Browse Classes',
                        style: TextStyles.buttonText.copyWith(
                          color: AppColors.primaryColor,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: Insets.m),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyles.body2.copyWith(
              color: AppColors.secondaryTextColor,
            ),
          ),
          Text(
            value,
            style: TextStyles.body2.copyWith(
              color: valueColor ?? AppColors.primaryTextColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }

  void _goToHome(BuildContext context, WidgetRef ref) {
    // Reset checkout state
    ref.read(subscriptionCheckoutProvider.notifier).reset();

    // Refresh home data to update membership status
    ref.read(homeNotifierProvider.notifier).refresh();

    // Navigate to home
    Navigator.of(context).popUntil((route) => route.isFirst);
  }
}
