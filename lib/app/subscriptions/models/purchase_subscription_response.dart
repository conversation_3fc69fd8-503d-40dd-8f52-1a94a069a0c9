import 'subscription.dart';

/// Purchase subscription response model that strictly follows the OpenAPI schema
class PurchaseSubscriptionResponse {
  final bool success;
  final String message;
  final PurchaseSubscriptionData data;
  final Map<String, dynamic>? errors;

  PurchaseSubscriptionResponse({
    required this.success,
    required this.message,
    required this.data,
    this.errors,
  });

  factory PurchaseSubscriptionResponse.fromJson(Map<String, dynamic> json) {
    return PurchaseSubscriptionResponse(
      success: json['success'],
      message: json['message'],
      data: PurchaseSubscriptionData.fromJson(json['data']),
      errors: json['errors'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data': data.toJson(),
      'errors': errors,
    };
  }
}

/// Purchase subscription data model
class PurchaseSubscriptionData {
  final Subscription subscription;
  final PaymentDetails paymentDetails;

  PurchaseSubscriptionData({
    required this.subscription,
    required this.paymentDetails,
  });

  factory PurchaseSubscriptionData.fromJson(Map<String, dynamic> json) {
    return PurchaseSubscriptionData(
      subscription: Subscription.fromJson(json['subscription']),
      paymentDetails: PaymentDetails.fromJson(json['payment_details']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'subscription': subscription.toJson(),
      'payment_details': paymentDetails.toJson(),
    };
  }
}

/// Payment details model
class PaymentDetails {
  final double amount;
  final String paymentMethod;
  final String? transactionId;

  PaymentDetails({
    required this.amount,
    required this.paymentMethod,
    this.transactionId,
  });

  factory PaymentDetails.fromJson(Map<String, dynamic> json) {
    return PaymentDetails(
      amount: (json['amount'] as num).toDouble(),
      paymentMethod: json['payment_method'],
      transactionId: json['transaction_id'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'amount': amount,
      'payment_method': paymentMethod,
      'transaction_id': transactionId,
    };
  }
}
