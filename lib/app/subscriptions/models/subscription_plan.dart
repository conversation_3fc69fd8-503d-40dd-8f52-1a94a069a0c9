import 'subscription_plan_feature.dart';

/// SubscriptionPlan model that strictly follows the OpenAPI schema
class SubscriptionPlan {
  final String id;
  final String name;
  final String? nameAlt;
  final String? description;
  final double price;
  final int duration; // Duration in days
  final int? classesPerWeek;
  final List<SubscriptionPlanFeature> features;
  final int freezeDaysCredit;
  final int freezeAttemptsCredit;
  final bool isActive;
  final DateTime createdAt;

  SubscriptionPlan({
    required this.id,
    required this.name,
    this.nameAlt,
    this.description,
    required this.price,
    required this.duration,
    this.classesPerWeek,
    required this.features,
    required this.freezeDaysCredit,
    required this.freezeAttemptsCredit,
    required this.isActive,
    required this.createdAt,
  });

  factory SubscriptionPlan.fromJson(Map<String, dynamic> json) {
    return SubscriptionPlan(
      id: json['id'],
      name: json['name'],
      nameAlt: json['name_alt'],
      description: json['description'],
      price: (json['price'] as num).toDouble(),
      duration: json['duration'],
      classesPerWeek: json['classes_per_week'],
      features:
          (json['features'] as List<dynamic>?)?.map((feature) => SubscriptionPlanFeature.fromJson(feature)).toList() ??
              [],
      freezeDaysCredit: json['freeze_days_credit'] ?? 0,
      freezeAttemptsCredit: json['freeze_attempts_credit'] ?? 0,
      isActive: json['is_active'] ?? true,
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'name_alt': nameAlt,
      'description': description,
      'price': price,
      'duration': duration,
      'classes_per_week': classesPerWeek,
      'features': features.map((feature) => feature.toJson()).toList(),
      'freeze_days_credit': freezeDaysCredit,
      'freeze_attempts_credit': freezeAttemptsCredit,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// Get duration in a human-readable format
  String get durationText {
    if (duration >= 365) {
      final years = (duration / 365).floor();
      return years == 1 ? '1 Year' : '$years Years';
    } else if (duration >= 30) {
      final months = (duration / 30).floor();
      return months == 1 ? '1 Month' : '$months Months';
    } else if (duration >= 7) {
      final weeks = (duration / 7).floor();
      return weeks == 1 ? '1 Week' : '$weeks Weeks';
    } else {
      return duration == 1 ? '1 Day' : '$duration Days';
    }
  }

  /// Get classes per week text
  String? get classesPerWeekText {
    if (classesPerWeek == null) return null;
    return classesPerWeek == 1 ? '1 Class per week' : '$classesPerWeek Classes per week';
  }
}
