/// SubscriptionPlanFeature model that strictly follows the OpenAPI schema
class SubscriptionPlanFeature {
  final String name;
  final String nameAlt;
  final String value;
  final bool? isNumeric;
  final bool? isPremium;
  final String? icon;

  SubscriptionPlanFeature({
    required this.name,
    required this.nameAlt,
    required this.value,
    this.isNumeric,
    this.isPremium,
    this.icon,
  });

  factory SubscriptionPlanFeature.fromJson(Map<String, dynamic> json) {
    return SubscriptionPlanFeature(
      name: json['name'],
      nameAlt: json['name_alt'],
      value: json['value'],
      isNumeric: json['isNumeric'],
      isPremium: json['isPremium'],
      icon: json['icon'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'name_alt': nameAlt,
      'value': value,
      'isNumeric': isNumeric,
      'isPremium': isPremium,
      'icon': icon,
    };
  }

  /// Get display text for the feature
  String get displayText {
    return nameAlt.isNotEmpty ? nameAlt : name;
  }

  /// Check if this is a premium feature
  bool get isHighlighted {
    return isPremium ?? false;
  }
}
