import 'subscription_plan.dart';

/// Subscription model that strictly follows the OpenAPI schema
class Subscription {
  final String id;
  final String memberId;
  final String planId;
  final String status; // active, paused, cancelled, expired, frozen, pending
  final DateTime startDate;
  final DateTime endDate;
  final bool autoRenew;
  final int freezeDaysCredit;
  final int freezeAttemptsCredit;
  final bool isFreezedToday;
  final SubscriptionPlan plan;
  final DateTime createdAt;

  Subscription({
    required this.id,
    required this.memberId,
    required this.planId,
    required this.status,
    required this.startDate,
    required this.endDate,
    required this.autoRenew,
    required this.freezeDaysCredit,
    required this.freezeAttemptsCredit,
    required this.isFreezedToday,
    required this.plan,
    required this.createdAt,
  });

  factory Subscription.fromJson(Map<String, dynamic> json) {
    return Subscription(
      id: json['id'],
      memberId: json['member_id'],
      planId: json['plan_id'],
      status: json['status'],
      startDate: DateTime.parse(json['start_date']),
      endDate: DateTime.parse(json['end_date']),
      autoRenew: json['auto_renew'] ?? false,
      freezeDaysCredit: json['freeze_days_credit'] ?? 0,
      freezeAttemptsCredit: json['freeze_attempts_credit'] ?? 0,
      isFreezedToday: json['is_freezed_today'] ?? false,
      plan: SubscriptionPlan.fromJson(json['plan']),
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'member_id': memberId,
      'plan_id': planId,
      'status': status,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'auto_renew': autoRenew,
      'freeze_days_credit': freezeDaysCredit,
      'freeze_attempts_credit': freezeAttemptsCredit,
      'is_freezed_today': isFreezedToday,
      'plan': plan.toJson(),
      'created_at': createdAt.toIso8601String(),
    };
  }

  // Status helper methods
  bool get isActive => status == 'active';
  bool get isPaused => status == 'paused';
  bool get isCancelled => status == 'cancelled';
  bool get isExpired => status == 'expired';
  bool get isFrozen => status == 'frozen';
  bool get isPending => status == 'pending';

  // Computed properties
  bool get isCurrentlyActive => isActive && !isExpired;
  bool get isExpiredByDate => DateTime.now().isAfter(endDate);

  int get daysRemaining {
    if (isExpiredByDate) return 0;
    return endDate.difference(DateTime.now()).inDays;
  }

  /// Get subscription plan name
  String get planName => plan.name;

  /// Get expiry date for display
  DateTime? get expiryDate => endDate;
}
