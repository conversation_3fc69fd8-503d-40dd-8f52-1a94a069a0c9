import 'package:flutter_test/flutter_test.dart';
import 'package:techrar_gym/app/subscriptions/models/subscription_plan.dart';
import 'package:techrar_gym/app/subscriptions/models/subscription_plan_feature.dart';

void main() {
  group('Subscription Flow Tests', () {
    test('SubscriptionPlan model should parse from JSON correctly', () {
      final json = {
        'id': 'plan_123',
        'name': 'Premium Plan',
        'name_alt': 'Premium Membership',
        'description': 'Full access to all facilities',
        'price': 99.99,
        'duration': 30,
        'classes_per_week': 5,
        'features': [
          {
            'name': 'gym_access',
            'name_alt': 'Gym Access',
            'value': 'Unlimited',
            'isNumeric': false,
            'isPremium': true,
            'icon': 'fitness_center'
          }
        ],
        'freeze_days_credit': 7,
        'freeze_attempts_credit': 2,
        'is_active': true,
        'created_at': '2024-01-01T00:00:00Z',
      };

      final plan = SubscriptionPlan.fromJson(json);

      expect(plan.id, 'plan_123');
      expect(plan.name, 'Premium Plan');
      expect(plan.nameAlt, 'Premium Membership');
      expect(plan.description, 'Full access to all facilities');
      expect(plan.price, 99.99);
      expect(plan.duration, 30);
      expect(plan.classesPerWeek, 5);
      expect(plan.freezeDaysCredit, 7);
      expect(plan.freezeAttemptsCredit, 2);
      expect(plan.isActive, true);
      expect(plan.features.length, 1);
      expect(plan.features.first.name, 'gym_access');
    });

    test('SubscriptionPlan should format duration correctly', () {
      final planDaily = SubscriptionPlan(
        id: '1',
        name: 'Daily',
        price: 10.0,
        duration: 1,
        features: [],
        freezeDaysCredit: 0,
        freezeAttemptsCredit: 0,
        isActive: true,
        createdAt: DateTime.now(),
      );

      final planWeekly = SubscriptionPlan(
        id: '2',
        name: 'Weekly',
        price: 50.0,
        duration: 7,
        features: [],
        freezeDaysCredit: 0,
        freezeAttemptsCredit: 0,
        isActive: true,
        createdAt: DateTime.now(),
      );

      final planMonthly = SubscriptionPlan(
        id: '3',
        name: 'Monthly',
        price: 100.0,
        duration: 30,
        features: [],
        freezeDaysCredit: 0,
        freezeAttemptsCredit: 0,
        isActive: true,
        createdAt: DateTime.now(),
      );

      final planYearly = SubscriptionPlan(
        id: '4',
        name: 'Yearly',
        price: 1000.0,
        duration: 365,
        features: [],
        freezeDaysCredit: 0,
        freezeAttemptsCredit: 0,
        isActive: true,
        createdAt: DateTime.now(),
      );

      expect(planDaily.durationText, '1 Day');
      expect(planWeekly.durationText, '1 Week');
      expect(planMonthly.durationText, '1 Month');
      expect(planYearly.durationText, '1 Year');
    });

    test('SubscriptionPlanFeature should parse correctly', () {
      final json = {
        'name': 'gym_access',
        'name_alt': 'Gym Access',
        'value': 'Unlimited',
        'isNumeric': false,
        'isPremium': true,
        'icon': 'fitness_center'
      };

      final feature = SubscriptionPlanFeature.fromJson(json);

      expect(feature.name, 'gym_access');
      expect(feature.nameAlt, 'Gym Access');
      expect(feature.value, 'Unlimited');
      expect(feature.isNumeric, false);
      expect(feature.isPremium, true);
      expect(feature.icon, 'fitness_center');
      expect(feature.displayText, 'Gym Access');
      expect(feature.isHighlighted, true);
    });
  });
}
